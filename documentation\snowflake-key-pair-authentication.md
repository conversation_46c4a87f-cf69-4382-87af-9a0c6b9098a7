# Snowflake Key Pair Authentication

This document describes the implementation and usage of key pair authentication for Snowflake connections in the Genesys Adapter.

## Overview

The Genesys Adapter uses Snowflake key pair authentication for all Snowflake connections. Password authentication is no longer supported. This provides enhanced security and is the only supported authentication method for Snowflake environments.

## Features

- **RSA Key Pair Authentication**: Support for RSA private/public key pairs
- **Encrypted Private Keys**: Support for password-protected private keys
- **Flexible Key Sources**: Private keys can be provided as files or embedded content
- **Automatic Key Processing**: Handles PEM format conversion and validation
- **Connection String Integration**: Seamless integration with existing connection string building

## Configuration

### Database Configuration

To use key pair authentication, configure your `Database` object with the following properties:

```csharp
var database = new Database
{
    Type = DatabaseType.Snowflake,
    Address = "your-account.snowflakecomputing.com",
    Port = 443,
    Name = "your_database",
    User = "your_username",
    Schema = "your_schema",
    AuthenticationMethod = AuthenticationMethod.KeyPair,
    
    // Option 1: Private key from file
    PrivateKeyFile = "/path/to/private_key.pem",
    
    // Option 2: Private key as content (alternative to file)
    PrivateKeyContent = "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----",
    
    // Optional: Passphrase for encrypted private keys
    PrivateKeyPassphrase = "your_passphrase"
};
```

### Configuration Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `AuthenticationMethod` | `AuthenticationMethod` | Yes | Must be set to `AuthenticationMethod.KeyPair` |
| `PrivateKeyFile` | `string` | Conditional | Path to the private key file (PEM format) |
| `PrivateKeyContent` | `string` | Conditional | Private key content as string (PEM format) |
| `PrivateKeyPassphrase` | `string` | No | Passphrase for encrypted private keys |

**Note**: Either `PrivateKeyFile` or `PrivateKeyContent` must be provided, but not both.

## Key Generation

### 1. Generate RSA Key Pair

```bash
# Generate private key (2048-bit RSA)
openssl genrsa -out private_key.pem 2048

# Generate public key from private key
openssl rsa -in private_key.pem -pubout -out public_key.pem
```

### 2. Generate Encrypted Private Key

```bash
# Generate encrypted private key
openssl genrsa -aes256 -out private_key_encrypted.pem 2048
```

### 3. Extract Public Key for Snowflake

```bash
# Get public key in the format required by Snowflake
openssl rsa -in private_key.pem -pubout -outform DER | openssl base64 -A
```

## Snowflake Setup

### 1. Create User with Public Key

```sql
-- Create user and assign public key
CREATE USER your_username 
RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...';

-- Grant necessary privileges
GRANT ROLE your_role TO USER your_username;
```

### 2. Verify Configuration

```sql
-- Check user configuration
DESCRIBE USER your_username;

-- Verify RSA_PUBLIC_KEY is set
SELECT * FROM INFORMATION_SCHEMA.USERS WHERE NAME = 'YOUR_USERNAME';
```

## Usage Examples

### Basic Usage

```csharp
using DBUtils;
using CSG.Adapter.Configuration;

// Configure database with key pair authentication
var database = new Database
{
    Type = DatabaseType.Snowflake,
    Address = "myaccount.snowflakecomputing.com",
    User = "myuser",
    Name = "mydatabase",
    Schema = "PUBLIC",
    AuthenticationMethod = AuthenticationMethod.KeyPair,
    PrivateKeyFile = "/path/to/private_key.pem"
};

// Create DBUtils instance
var dbUtils = new DBUtils(logger);

// Build connection string
var connectionString = dbUtils.BuildConnectionString(database);

// Connect to database
dbUtils.DBType = database.Type;
dbUtils.DBConnectionString = connectionString;
dbUtils.ConnectToDatabase();
```

### Using Encrypted Private Key

```csharp
var database = new Database
{
    Type = DatabaseType.Snowflake,
    Address = "myaccount.snowflakecomputing.com",
    User = "myuser",
    Name = "mydatabase",
    Schema = "PUBLIC",
    AuthenticationMethod = AuthenticationMethod.KeyPair,
    PrivateKeyFile = "/path/to/encrypted_private_key.pem",
    PrivateKeyPassphrase = "my_secure_passphrase"
};
```

### Using Private Key Content

```csharp
var privateKeyContent = File.ReadAllText("/path/to/private_key.pem");

var database = new Database
{
    Type = DatabaseType.Snowflake,
    Address = "myaccount.snowflakecomputing.com",
    User = "myuser",
    Name = "mydatabase",
    Schema = "PUBLIC",
    AuthenticationMethod = AuthenticationMethod.KeyPair,
    PrivateKeyContent = privateKeyContent
};
```

## Implementation Details

### Key Components

1. **SnowflakeKeyPairUtils**: Utility class for key pair operations
   - `ValidateKeyPairConfiguration()`: Validates configuration
   - `ProcessPrivateKey()`: Processes and converts private keys
   - `LoadPrivateKeyFromFile()`: Loads private key from file
   - `ConvertToPemFormat()`: Converts keys to PEM format

2. **DBUtils Extensions**: Enhanced connection string building
   - Automatic detection of key pair authentication
   - Integration with Snowflake.Data connection string builder
   - Support for both file-based and content-based private keys

### Connection String Format

The generated connection string includes:

```
ACCOUNT=myaccount;USER=myuser;DATABASE=mydatabase;SCHEMA=PUBLIC;AUTHENTICATOR=snowflake_jwt;PRIVATE_KEY=<base64_encoded_key>;PRIVATE_KEY_PWD=<passphrase>
```

### Error Handling

The implementation includes comprehensive error handling for:

- Missing or invalid private key files
- Malformed private key content
- Incorrect passphrases for encrypted keys
- Invalid PEM format
- Snowflake connection errors

## Testing

### Unit Tests

The implementation includes comprehensive unit tests covering:

- Connection string building with various configurations
- Private key processing and validation
- Error handling for invalid configurations
- PEM format conversion

### Integration Tests

Integration tests are available but require actual Snowflake credentials:

```bash
# Set environment variables for integration tests
export SNOWFLAKE_ACCOUNT="your-account"
export SNOWFLAKE_USER="your-user"
export SNOWFLAKE_DATABASE="your-database"
export SNOWFLAKE_SCHEMA="your-schema"
export SNOWFLAKE_PRIVATE_KEY_FILE="/path/to/private_key.pem"
export SNOWFLAKE_PRIVATE_KEY_PASSPHRASE="your-passphrase"  # Optional

# Run integration tests
dotnet test --filter "FullyQualifiedName~SnowflakeIntegration"
```

## Security Considerations

1. **Private Key Protection**: Store private keys securely and restrict file permissions
2. **Passphrase Management**: Use strong passphrases for encrypted private keys
3. **Key Rotation**: Regularly rotate key pairs and update Snowflake user configuration
4. **Environment Variables**: Consider using environment variables for sensitive configuration
5. **Logging**: Private keys and passphrases are never logged

## Troubleshooting

### Common Issues

1. **"Private key file not found"**
   - Verify the file path is correct and accessible
   - Check file permissions

2. **"Invalid private key format"**
   - Ensure the private key is in PEM format
   - Verify the key is not corrupted

3. **"Authentication failed"**
   - Verify the public key is correctly configured in Snowflake
   - Check that the user has necessary privileges
   - Ensure the private key matches the public key in Snowflake

4. **"Incorrect passphrase"**
   - Verify the passphrase for encrypted private keys
   - Ensure the private key is actually encrypted if providing a passphrase

### Debug Steps

1. Validate configuration using `SnowflakeKeyPairUtils.ValidateKeyPairConfiguration()`
2. Check Snowflake user configuration with `DESCRIBE USER`
3. Verify connection string format
4. Enable detailed logging for connection attempts

## Migration from Password Authentication

To migrate from password to key pair authentication:

1. Generate RSA key pair
2. Configure public key in Snowflake user
3. Update application configuration to use `AuthenticationMethod.KeyPair`
4. Remove password from configuration
5. Test connection with new authentication method
6. Deploy updated configuration

## Best Practices

1. Use 2048-bit or higher RSA keys
2. Encrypt private keys with strong passphrases
3. Store private keys in secure locations with restricted access
4. Use configuration management systems for key distribution
5. Implement key rotation procedures
6. Monitor authentication logs for security events
7. Use separate keys for different environments (dev, staging, prod)

## Quick Reference

### Configuration Example

```json
{
  "Database": {
    "Type": "Snowflake",
    "Address": "myaccount.snowflakecomputing.com",
    "User": "myuser",
    "Name": "mydatabase",
    "Schema": "PUBLIC",
    "AuthenticationMethod": "KeyPair",
    "PrivateKeyFile": "/path/to/private_key.pem",
    "PrivateKeyPassphrase": "optional_passphrase"
  }
}
```

### Key Generation Commands

```bash
# Generate private key
openssl genrsa -out private_key.pem 2048

# Generate public key
openssl rsa -in private_key.pem -pubout -out public_key.pem

# Get public key for Snowflake
openssl rsa -in private_key.pem -pubout -outform DER | openssl base64 -A
```

### Snowflake User Setup

```sql
-- Create user with public key
CREATE USER myuser RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...';

-- Grant role
GRANT ROLE myrole TO USER myuser;
```

## Related Files

- `DBUtils/DBUtils.cs` - Main connection string building logic
- `DBUtils/SnowflakeKeyPairUtils.cs` - Key pair utility functions
- `CSG.Adapter.Configuration/Database.cs` - Configuration model
- `tests/SnowflakeKeyPairTests.cs` - Unit tests
- `tests/SnowflakeIntegrationTests.cs` - Integration tests
