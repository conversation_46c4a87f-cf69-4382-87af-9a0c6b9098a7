using System;
using System.IO;
using Xunit;
using DBUtils;
using CSG.Adapter.Configuration;
using Microsoft.Extensions.Logging;
using Moq;

namespace GenesysAdapter.Tests
{
    public class SnowflakeKeyPairAuthTests
    {
        private readonly Mock<ILogger> _mockLogger;

        public SnowflakeKeyPairAuthTests()
        {
            _mockLogger = new Mock<ILogger>();
        }

        [Fact]
        public void ValidatePrivateKeyFormat_ValidUnencryptedKey_ReturnsTrue()
        {
            // Arrange
            var validPrivateKey = @"-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wQNneCjGQJw2TgHdU69SGye2OLUuycJAcMzOMUIzJXLoJOqcwdXKtsI4pH3F6uL8
WqKaki5wxqjRJXGR0gDO58+bM6m3xbwVfyYSEjMdyCHdihFBuIcSJdIHHBrHQy+
oQXsWlLuBfLEr9lY4BVuKFbBjuFN2TreSBDHiGWvMRiKUBo1su8tzrbfKoTboyP
jkVVdJioVTMLDdbHA2VdOiN2btinlnBjHPxFOuHbigFMhqtAdLgMmDkuM6+14=
-----END PRIVATE KEY-----";

            // Act
            var result = SnowflakeKeyPairUtils.ValidatePrivateKeyFormat(validPrivateKey);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidatePrivateKeyFormat_ValidEncryptedKey_ReturnsTrue()
        {
            // Arrange
            var validEncryptedKey = @"-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFHDBOBgkqhkiG9w0BBQ0wQTApBgkqhkiG9w0BBQwwHAQIxK967BYxVp0CAggA
MAwGCCqGSIb3DQIJAQUAMBQGCCqGSIb3DQMHBAhT3+7DWJ56DEEEggTYMIIE1AYJ
KoZIhvcNAQcBoIIEwQSCBL0wggS5MIIEtQYLKoZIhvcNAQwKAQKgggTuMIIE6jAc
BgoqhkiG9w0BDAEDMA4ECBtxuFLRXMstAgIIAASCBMg5QwxvLMiZstEcBelFaUlJ
-----END ENCRYPTED PRIVATE KEY-----";

            // Act
            var result = SnowflakeKeyPairUtils.ValidatePrivateKeyFormat(validEncryptedKey);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidatePrivateKeyFormat_InvalidKey_ReturnsFalse()
        {
            // Arrange
            var invalidKey = "This is not a valid private key";

            // Act
            var result = SnowflakeKeyPairUtils.ValidatePrivateKeyFormat(invalidKey);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidatePrivateKeyFormat_EmptyString_ReturnsFalse()
        {
            // Act
            var result = SnowflakeKeyPairUtils.ValidatePrivateKeyFormat("");

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidatePrivateKeyFormat_NullString_ReturnsFalse()
        {
            // Act
            var result = SnowflakeKeyPairUtils.ValidatePrivateKeyFormat(null);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateKeyPairConfiguration_ValidConfiguration_ReturnsTrue()
        {
            // Arrange
            var config = new Database
            {
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyContent = @"-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
-----END PRIVATE KEY-----"
            };

            // Act
            var result = SnowflakeKeyPairUtils.ValidateKeyPairConfiguration(config, _mockLogger.Object);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidateKeyPairConfiguration_BothFileAndContent_ReturnsFalse()
        {
            // Arrange
            var config = new Database
            {
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyFile = "/path/to/key.p8",
                PrivateKeyContent = "-----BEGIN PRIVATE KEY-----\nkey content\n-----END PRIVATE KEY-----"
            };

            // Act
            var result = SnowflakeKeyPairUtils.ValidateKeyPairConfiguration(config, _mockLogger.Object);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateKeyPairConfiguration_NoKeyProvided_ReturnsFalse()
        {
            // Arrange
            var config = new Database
            {
                AuthenticationMethod = AuthenticationMethod.KeyPair
            };

            // Act
            var result = SnowflakeKeyPairUtils.ValidateKeyPairConfiguration(config, _mockLogger.Object);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateKeyPairConfiguration_WrongAuthMethod_ReturnsFalse()
        {
            // Arrange
            var config = new Database
            {
                AuthenticationMethod = AuthenticationMethod.Password,
                PrivateKeyContent = "-----BEGIN PRIVATE KEY-----\nkey content\n-----END PRIVATE KEY-----"
            };

            // Act
            var result = SnowflakeKeyPairUtils.ValidateKeyPairConfiguration(config, _mockLogger.Object);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsPrivateKeyEncrypted_EncryptedKey_ReturnsTrue()
        {
            // Arrange
            var encryptedKey = "-----BEGIN ENCRYPTED PRIVATE KEY-----\nkey content\n-----END ENCRYPTED PRIVATE KEY-----";

            // Act
            var result = SnowflakeKeyPairUtils.IsPrivateKeyEncrypted(encryptedKey);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsPrivateKeyEncrypted_UnencryptedKey_ReturnsFalse()
        {
            // Arrange
            var unencryptedKey = "-----BEGIN PRIVATE KEY-----\nkey content\n-----END PRIVATE KEY-----";

            // Act
            var result = SnowflakeKeyPairUtils.IsPrivateKeyEncrypted(unencryptedKey);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsLegacyPasswordConfiguration_NullAuthMethod_ReturnsTrue()
        {
            // Arrange
            var config = new Database
            {
                AuthenticationMethod = null
            };

            // Act
            var result = SnowflakeKeyPairUtils.IsLegacyPasswordConfiguration(config);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsLegacyPasswordConfiguration_PasswordAuthMethod_ReturnsTrue()
        {
            // Arrange
            var config = new Database
            {
                AuthenticationMethod = AuthenticationMethod.Password
            };

            // Act
            var result = SnowflakeKeyPairUtils.IsLegacyPasswordConfiguration(config);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsLegacyPasswordConfiguration_KeyPairAuthMethod_ReturnsFalse()
        {
            // Arrange
            var config = new Database
            {
                AuthenticationMethod = AuthenticationMethod.KeyPair
            };

            // Act
            var result = SnowflakeKeyPairUtils.IsLegacyPasswordConfiguration(config);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateConfigurationMigration_SameAuthMethod_ReturnsTrue()
        {
            // Arrange
            var currentConfig = new Database { AuthenticationMethod = AuthenticationMethod.Password };
            var newConfig = new Database { AuthenticationMethod = AuthenticationMethod.Password };

            // Act
            var result = SnowflakeKeyPairUtils.ValidateConfigurationMigration(currentConfig, newConfig, _mockLogger.Object);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidateConfigurationMigration_ValidKeyPairMigration_ReturnsTrue()
        {
            // Arrange
            var currentConfig = new Database { AuthenticationMethod = AuthenticationMethod.Password };
            var newConfig = new Database 
            { 
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyContent = "-----BEGIN PRIVATE KEY-----\nvalid key\n-----END PRIVATE KEY-----"
            };

            // Act
            var result = SnowflakeKeyPairUtils.ValidateConfigurationMigration(currentConfig, newConfig, _mockLogger.Object);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void GenerateKeyPairInstructions_ReturnsNonEmptyString()
        {
            // Act
            var instructions = SnowflakeKeyPairUtils.GenerateKeyPairInstructions();

            // Assert
            Assert.NotNull(instructions);
            Assert.NotEmpty(instructions);
            Assert.Contains("openssl", instructions);
            Assert.Contains("RSA_PUBLIC_KEY", instructions);
        }

        [Fact]
        public void GetMigrationGuidance_ReturnsNonEmptyString()
        {
            // Act
            var guidance = SnowflakeKeyPairUtils.GetMigrationGuidance();

            // Assert
            Assert.NotNull(guidance);
            Assert.NotEmpty(guidance);
            Assert.Contains("Migration", guidance);
            Assert.Contains("Step", guidance);
        }

        [Fact]
        public void GetSecurityRecommendations_ReturnsRecommendations()
        {
            // Act
            var recommendations = SnowflakeKeyPairUtils.GetSecurityRecommendations();

            // Assert
            Assert.NotNull(recommendations);
            Assert.NotEmpty(recommendations);
            Assert.Contains(recommendations, r => r.Contains("2048-bit"));
            Assert.Contains(recommendations, r => r.Contains("passphrase"));
        }
    }
}
