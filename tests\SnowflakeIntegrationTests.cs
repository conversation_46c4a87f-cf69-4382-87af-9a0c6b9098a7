using System;
using System.IO;
using Xunit;
using DBUtils;
using CSG.Adapter.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System.Data;

namespace GenesysAdapter.Tests
{
    /// <summary>
    /// Integration tests for Snowflake key pair authentication.
    /// These tests require actual Snowflake credentials and are skipped by default.
    /// Set environment variables to enable these tests:
    /// - SNOWFLAKE_ACCOUNT: Your Snowflake account
    /// - SNOWFLAKE_USER: Your Snowflake user
    /// - SNOWFLAKE_DATABASE: Your test database
    /// - SNOWFLAKE_SCHEMA: Your test schema
    /// - SNOWFLAKE_PRIVATE_KEY_FILE: Path to private key file
    /// - SNOWFLAKE_PRIVATE_KEY_PASSPHRASE: Passphrase (if encrypted)
    /// </summary>
    public class SnowflakeIntegrationTests : IDisposable
    {
        private readonly Mock<ILogger<DBUtils.DBUtils>> _mockLogger;
        private readonly DBUtils.DBUtils _dbUtils;
        private readonly bool _integrationTestsEnabled;
        private readonly Database? _testConfig;

        public SnowflakeIntegrationTests()
        {
            _mockLogger = new Mock<ILogger<DBUtils.DBUtils>>();
            _dbUtils = new DBUtils.DBUtils(_mockLogger.Object);
            
            // Check if integration tests are enabled via environment variables
            _integrationTestsEnabled = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("SNOWFLAKE_ACCOUNT"));
            
            if (_integrationTestsEnabled)
            {
                _testConfig = CreateTestConfiguration();
            }
        }

        private Database CreateTestConfiguration()
        {
            return new Database
            {
                Type = DatabaseType.Snowflake,
                Address = Environment.GetEnvironmentVariable("SNOWFLAKE_ACCOUNT"),
                Port = 443,
                Name = Environment.GetEnvironmentVariable("SNOWFLAKE_DATABASE") ?? "TEST_DB",
                User = Environment.GetEnvironmentVariable("SNOWFLAKE_USER"),
                Schema = Environment.GetEnvironmentVariable("SNOWFLAKE_SCHEMA") ?? "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyFile = Environment.GetEnvironmentVariable("SNOWFLAKE_PRIVATE_KEY_FILE"),
                PrivateKeyPassphrase = Environment.GetEnvironmentVariable("SNOWFLAKE_PRIVATE_KEY_PASSPHRASE")
            };
        }

        [Fact]
        public void KeyPairAuthentication_ValidCredentials_CanConnect()
        {
            // Skip if integration tests are not enabled
            if (!_integrationTestsEnabled)
            {
                Skip.If(true, "Integration tests disabled. Set SNOWFLAKE_ACCOUNT environment variable to enable.");
                return;
            }

            // Arrange
            Assert.NotNull(_testConfig);

            // Validate configuration
            var isValid = SnowflakeKeyPairUtils.ValidateKeyPairConfiguration(_testConfig, _mockLogger.Object);
            Assert.True(isValid, "Test configuration should be valid");

            // Act & Assert - This will throw if connection fails
            var connectionString = _dbUtils.BuildConnectionString(_testConfig);
            Assert.NotNull(connectionString);
            Assert.Contains("AUTHENTICATOR=snowflake_jwt", connectionString);

            // Test actual connection using DBUtils
            _dbUtils.DBType = _testConfig.Type ?? DatabaseType.Snowflake;
            _dbUtils.DBConnectionString = connectionString;
            _dbUtils.ConnectToDatabase();
            Assert.Equal(ConnectionState.Open, _dbUtils.DBConnect.State);
        }

        [Fact]
        public void KeyPairAuthentication_EncryptedPrivateKey_CanConnect()
        {
            // Skip if integration tests are not enabled or no passphrase provided
            if (!_integrationTestsEnabled || string.IsNullOrEmpty(Environment.GetEnvironmentVariable("SNOWFLAKE_PRIVATE_KEY_PASSPHRASE")))
            {
                Skip.If(true, "Integration tests disabled or no encrypted key passphrase provided.");
                return;
            }

            // Arrange
            Assert.NotNull(_testConfig);
            Assert.NotNull(_testConfig.PrivateKeyPassphrase);

            // Act & Assert
            var connectionString = _dbUtils.BuildConnectionString(_testConfig);
            Assert.Contains("PRIVATE_KEY_PWD=", connectionString);

            // Test actual connection using DBUtils
            _dbUtils.DBType = _testConfig.Type ?? DatabaseType.Snowflake;
            _dbUtils.DBConnectionString = connectionString;
            _dbUtils.ConnectToDatabase();
            Assert.Equal(ConnectionState.Open, _dbUtils.DBConnect.State);
        }

        [Fact]
        public void KeyPairAuthentication_WithPrivateKeyContent_CanConnect()
        {
            // Skip if integration tests are not enabled
            if (!_integrationTestsEnabled)
            {
                Skip.If(true, "Integration tests disabled. Set SNOWFLAKE_ACCOUNT environment variable to enable.");
                return;
            }

            // Arrange - Create config with private key content instead of file
            var privateKeyFile = Environment.GetEnvironmentVariable("SNOWFLAKE_PRIVATE_KEY_FILE");
            if (string.IsNullOrEmpty(privateKeyFile) || !File.Exists(privateKeyFile))
            {
                Skip.If(true, "Private key file not found for content test.");
                return;
            }

            var privateKeyContent = File.ReadAllText(privateKeyFile);
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = Environment.GetEnvironmentVariable("SNOWFLAKE_ACCOUNT"),
                Port = 443,
                Name = Environment.GetEnvironmentVariable("SNOWFLAKE_DATABASE") ?? "TEST_DB",
                User = Environment.GetEnvironmentVariable("SNOWFLAKE_USER"),
                Schema = Environment.GetEnvironmentVariable("SNOWFLAKE_SCHEMA") ?? "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyContent = privateKeyContent,
                PrivateKeyPassphrase = Environment.GetEnvironmentVariable("SNOWFLAKE_PRIVATE_KEY_PASSPHRASE")
            };

            // Act & Assert
            var connectionString = _dbUtils.BuildConnectionString(config);
            Assert.Contains("PRIVATE_KEY=", connectionString);
            Assert.DoesNotContain("PRIVATE_KEY_FILE=", connectionString);

            // Test actual connection using DBUtils
            _dbUtils.DBType = config.Type ?? DatabaseType.Snowflake;
            _dbUtils.DBConnectionString = connectionString;
            _dbUtils.ConnectToDatabase();
            Assert.Equal(ConnectionState.Open, _dbUtils.DBConnect.State);
        }

        [Fact]
        public void KeyPairAuthentication_ExecuteSimpleQuery_Success()
        {
            // Skip if integration tests are not enabled
            if (!_integrationTestsEnabled)
            {
                Skip.If(true, "Integration tests disabled. Set SNOWFLAKE_ACCOUNT environment variable to enable.");
                return;
            }

            // Arrange
            Assert.NotNull(_testConfig);

            // Act & Assert
            _dbUtils.DBType = _testConfig.Type ?? DatabaseType.Snowflake;
            _dbUtils.DBConnectionString = _dbUtils.BuildConnectionString(_testConfig);
            _dbUtils.ConnectToDatabase();

            using var command = _dbUtils.DBConnect.CreateCommand();
            command.CommandText = "SELECT CURRENT_VERSION() as version";

            var result = command.ExecuteScalar();
            Assert.NotNull(result);
            Assert.IsType<string>(result);

            var version = result.ToString();
            Assert.NotEmpty(version);
            Assert.Contains(".", version); // Version should contain dots
        }

        [Fact]
        public void KeyPairAuthentication_ParseConnectionString_Success()
        {
            // Skip if integration tests are not enabled
            if (!_integrationTestsEnabled)
            {
                Skip.If(true, "Integration tests disabled. Set SNOWFLAKE_ACCOUNT environment variable to enable.");
                return;
            }

            // Arrange
            Assert.NotNull(_testConfig);
            var originalConnectionString = _dbUtils.BuildConnectionString(_testConfig);

            // Act - Parse the connection string back to configuration
            var parsedConfig = new Database();
            _dbUtils.ParseConnectionString(parsedConfig, originalConnectionString);

            // Assert
            Assert.Equal(_testConfig.Type, parsedConfig.Type);
            Assert.Equal(_testConfig.User, parsedConfig.User);
            Assert.Equal(_testConfig.Name, parsedConfig.Name);
            Assert.Equal(_testConfig.Schema, parsedConfig.Schema);
            Assert.Equal(AuthenticationMethod.KeyPair, parsedConfig.AuthenticationMethod);
            
            if (!string.IsNullOrEmpty(_testConfig.PrivateKeyFile))
            {
                Assert.Equal(_testConfig.PrivateKeyFile, parsedConfig.PrivateKeyFile);
            }
            
            if (!string.IsNullOrEmpty(_testConfig.PrivateKeyContent))
            {
                Assert.Equal(_testConfig.PrivateKeyContent, parsedConfig.PrivateKeyContent);
            }
        }

        [Fact]
        public void KeyPairAuthentication_InvalidPrivateKey_ThrowsException()
        {
            // Skip if integration tests are not enabled
            if (!_integrationTestsEnabled)
            {
                Skip.If(true, "Integration tests disabled. Set SNOWFLAKE_ACCOUNT environment variable to enable.");
                return;
            }

            // Arrange - Create config with invalid private key
            var invalidConfig = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = Environment.GetEnvironmentVariable("SNOWFLAKE_ACCOUNT"),
                Port = 443,
                Name = Environment.GetEnvironmentVariable("SNOWFLAKE_DATABASE") ?? "TEST_DB",
                User = Environment.GetEnvironmentVariable("SNOWFLAKE_USER"),
                Schema = Environment.GetEnvironmentVariable("SNOWFLAKE_SCHEMA") ?? "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyContent = "invalid-private-key-content"
            };

            // Act & Assert
            Assert.Throws<Exception>(() =>
            {
                _dbUtils.DBType = invalidConfig.Type ?? DatabaseType.Snowflake;
                _dbUtils.DBConnectionString = _dbUtils.BuildConnectionString(invalidConfig);
                _dbUtils.ConnectToDatabase();
            });
        }

        [Fact]
        public void KeyPairAuthentication_MissingPrivateKey_ThrowsArgumentException()
        {
            // Arrange - Create config without private key
            var invalidConfig = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "test.snowflakecomputing.com",
                Port = 443,
                Name = "TEST_DB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.KeyPair
                // No PrivateKeyFile or PrivateKeyContent
            };

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _dbUtils.BuildConnectionString(invalidConfig));
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }

    /// <summary>
    /// Helper class for skipping tests conditionally
    /// </summary>
    public static class Skip
    {
        public static void If(bool condition, string reason)
        {
            if (condition)
            {
                throw new SkipException(reason);
            }
        }
    }

    /// <summary>
    /// Exception thrown to skip a test
    /// </summary>
    public class SkipException : Exception
    {
        public SkipException(string reason) : base(reason) { }
    }
}
