using System;
using Xunit;
using DBUtils;
using CSG.Adapter.Configuration;
using Microsoft.Extensions.Logging;
using Moq;

namespace GenesysAdapter.Tests
{
    public class SnowflakeConnectionStringTests
    {
        private readonly Mock<ILogger<DBUtils.DBUtils>> _mockLogger;
        private readonly DBUtils.DBUtils _dbUtils;

        public SnowflakeConnectionStringTests()
        {
            _mockLogger = new Mock<ILogger<DBUtils.DBUtils>>();
            _dbUtils = new DBUtils.DBUtils(_mockLogger.Object);
        }

        [Fact]
        public void BuildConnectionString_PasswordAuthentication_ThrowsNotSupportedException()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Password = "testpassword",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.Password
            };

            // Act & Assert
            var exception = Assert.Throws<NotSupportedException>(() => _dbUtils.BuildConnectionString(config));
            Assert.Contains("Password authentication is no longer supported for Snowflake", exception.Message);
            Assert.Contains("Please use key pair authentication instead", exception.Message);
        }

        [Fact]
        public void BuildConnectionString_KeyPairAuthenticationWithFile_Success()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyFile = "/path/to/private_key.p8"
            };

            // Act
            var connectionString = _dbUtils.BuildConnectionString(config);

            // Assert
            Assert.NotNull(connectionString);
            Assert.Contains("AUTHENTICATOR=snowflake_jwt", connectionString);
            Assert.Contains("PRIVATE_KEY_FILE=/path/to/private_key.p8", connectionString);
            Assert.DoesNotContain("Password", connectionString);
        }

        [Fact]
        public void BuildConnectionString_KeyPairAuthenticationWithContent_Success()
        {
            // Arrange
            var privateKeyContent = "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\n-----END PRIVATE KEY-----";
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyContent = privateKeyContent
            };

            // Act
            var connectionString = _dbUtils.BuildConnectionString(config);

            // Assert
            Assert.NotNull(connectionString);
            Assert.Contains("AUTHENTICATOR=snowflake_jwt", connectionString);
            Assert.Contains("PRIVATE_KEY=", connectionString);
            Assert.DoesNotContain("Password", connectionString);
        }

        [Fact]
        public void BuildConnectionString_KeyPairAuthenticationWithPassphrase_Success()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyFile = "/path/to/private_key.p8",
                PrivateKeyPassphrase = "mypassphrase"
            };

            // Act
            var connectionString = _dbUtils.BuildConnectionString(config);

            // Assert
            Assert.NotNull(connectionString);
            Assert.Contains("AUTHENTICATOR=snowflake_jwt", connectionString);
            Assert.Contains("PRIVATE_KEY_FILE=/path/to/private_key.p8", connectionString);
            Assert.Contains("PRIVATE_KEY_PWD=", connectionString);
        }

        [Fact]
        public void BuildConnectionString_OAuthAuthentication_Success()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.OAuth,
                OAuthToken = "oauth_token_here"
            };

            // Act
            var connectionString = _dbUtils.BuildConnectionString(config);

            // Assert
            Assert.NotNull(connectionString);
            Assert.Contains("AUTHENTICATOR=oauth", connectionString);
            Assert.Contains("TOKEN=oauth_token_here", connectionString);
            Assert.DoesNotContain("Password", connectionString);
        }

        [Fact]
        public void BuildConnectionString_ExternalBrowserAuthentication_Success()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.ExternalBrowser
            };

            // Act
            var connectionString = _dbUtils.BuildConnectionString(config);

            // Assert
            Assert.NotNull(connectionString);
            Assert.Contains("AUTHENTICATOR=externalbrowser", connectionString);
            Assert.DoesNotContain("Password", connectionString);
            Assert.DoesNotContain("TOKEN", connectionString);
        }

        [Fact]
        public void BuildConnectionString_DefaultAuthenticationMethod_UsesKeyPair()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                PrivateKeyFile = "/path/to/private/key.pem"
                // AuthenticationMethod is null, should default to KeyPair
            };

            // Act
            var connectionString = _dbUtils.BuildConnectionString(config);

            // Assert
            Assert.NotNull(connectionString);
            Assert.Contains("AUTHENTICATOR=snowflake_jwt", connectionString);
            Assert.Contains("PRIVATE_KEY_FILE=/path/to/private/key.pem", connectionString);
            Assert.DoesNotContain("Password=", connectionString);
        }

        [Fact]
        public void BuildConnectionString_KeyPairWithoutPrivateKey_ThrowsException()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.KeyPair
                // No PrivateKeyFile or PrivateKeyContent
            };

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _dbUtils.BuildConnectionString(config));
        }

        [Fact]
        public void BuildConnectionString_KeyPairWithBothFileAndContent_ThrowsException()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.KeyPair,
                PrivateKeyFile = "/path/to/key.p8",
                PrivateKeyContent = "-----BEGIN PRIVATE KEY-----\nkey\n-----END PRIVATE KEY-----"
            };

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _dbUtils.BuildConnectionString(config));
        }

        [Fact]
        public void BuildConnectionString_OAuthWithoutToken_ThrowsException()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.OAuth
                // No OAuthToken
            };

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _dbUtils.BuildConnectionString(config));
        }

        [Fact]
        public void BuildConnectionString_PasswordAuthWithoutPassword_ThrowsNotSupportedException()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.Snowflake,
                Address = "myaccount.snowflakecomputing.com",
                Port = 443,
                Name = "TESTDB",
                User = "testuser",
                Schema = "PUBLIC",
                AuthenticationMethod = AuthenticationMethod.Password
                // No Password
            };

            // Act & Assert
            var exception = Assert.Throws<NotSupportedException>(() => _dbUtils.BuildConnectionString(config));
            Assert.Contains("Password authentication is no longer supported for Snowflake", exception.Message);
        }

        [Fact]
        public void BuildConnectionString_NonSnowflakeWithoutPassword_ThrowsException()
        {
            // Arrange
            var config = new Database
            {
                Type = DatabaseType.MSSQL,
                Address = "localhost",
                Port = 1433,
                Name = "TESTDB",
                User = "testuser"
                // No Password - should still require password for non-Snowflake
            };

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _dbUtils.BuildConnectionString(config));
        }
    }
}
