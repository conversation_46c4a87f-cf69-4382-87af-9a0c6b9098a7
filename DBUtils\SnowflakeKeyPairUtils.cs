using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using CSG.Adapter.Configuration;
using Microsoft.Extensions.Logging;

namespace DBUtils
{
    /// <summary>
    /// Utility class for Snowflake key pair authentication management
    /// </summary>
    public static class SnowflakeKeyPairUtils
    {
        private static readonly Regex PrivateKeyPattern = new Regex(
            @"-----BEGIN (ENCRYPTED )?PRIVATE KEY-----.*-----END (ENCRYPTED )?PRIVATE KEY-----",
            RegexOptions.Singleline | RegexOptions.IgnoreCase);

        private static readonly Regex PublicKeyPattern = new Regex(
            @"-----BEGIN PUBLIC KEY-----.*-----END PUBLIC KEY-----",
            RegexOptions.Singleline | RegexOptions.IgnoreCase);

        /// <summary>
        /// Validates the format of a private key string
        /// </summary>
        /// <param name="privateKey">The private key content to validate</param>
        /// <returns>True if the private key format is valid</returns>
        public static bool ValidatePrivateKeyFormat(string privateKey)
        {
            if (string.IsNullOrWhiteSpace(privateKey))
                return false;

            return PrivateKeyPattern.IsMatch(privateKey.Trim());
        }

        /// <summary>
        /// Validates that a private key file exists and has the correct format
        /// </summary>
        /// <param name="filePath">Path to the private key file</param>
        /// <returns>True if the file exists and contains a valid private key</returns>
        public static bool ValidatePrivateKeyFile(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            try
            {
                if (!File.Exists(filePath))
                    return false;

                string content = File.ReadAllText(filePath);
                return ValidatePrivateKeyFormat(content);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Validates the format of a public key string
        /// </summary>
        /// <param name="publicKey">The public key content to validate</param>
        /// <returns>True if the public key format is valid</returns>
        public static bool ValidatePublicKeyFormat(string publicKey)
        {
            if (string.IsNullOrWhiteSpace(publicKey))
                return false;

            return PublicKeyPattern.IsMatch(publicKey.Trim());
        }

        /// <summary>
        /// Validates the complete key pair configuration for Snowflake
        /// </summary>
        /// <param name="databaseOptions">Database configuration options</param>
        /// <param name="logger">Optional logger for validation messages</param>
        /// <returns>True if the configuration is valid</returns>
        public static bool ValidateKeyPairConfiguration(Database databaseOptions, ILogger? logger = null)
        {
            if (databaseOptions.AuthenticationMethod != AuthenticationMethod.KeyPair)
            {
                logger?.LogWarning("Authentication method is not set to KeyPair");
                return false;
            }

            // Check that either private key file or content is provided, but not both
            bool hasFile = !string.IsNullOrEmpty(databaseOptions.PrivateKeyFile);
            bool hasContent = !string.IsNullOrEmpty(databaseOptions.PrivateKeyContent);

            if (!hasFile && !hasContent)
            {
                logger?.LogError("Either PrivateKeyFile or PrivateKeyContent must be specified for key pair authentication");
                return false;
            }

            if (hasFile && hasContent)
            {
                logger?.LogError("Cannot specify both PrivateKeyFile and PrivateKeyContent. Choose one method");
                return false;
            }

            // Validate private key file if specified
            if (hasFile)
            {
                if (!ValidatePrivateKeyFile(databaseOptions.PrivateKeyFile!))
                {
                    logger?.LogError("Private key file is invalid or does not exist: {PrivateKeyFile}", databaseOptions.PrivateKeyFile);
                    return false;
                }
                logger?.LogDebug("Private key file validation passed: {PrivateKeyFile}", databaseOptions.PrivateKeyFile);
            }

            // Validate private key content if specified
            if (hasContent)
            {
                if (!ValidatePrivateKeyFormat(databaseOptions.PrivateKeyContent!))
                {
                    logger?.LogError("Private key content format is invalid");
                    return false;
                }
                logger?.LogDebug("Private key content validation passed");
            }

            return true;
        }

        /// <summary>
        /// Generates instructions for creating RSA key pairs for Snowflake authentication
        /// </summary>
        /// <returns>Detailed instructions for key pair generation</returns>
        public static string GenerateKeyPairInstructions()
        {
            var instructions = new StringBuilder();
            instructions.AppendLine("Snowflake Key Pair Authentication Setup Instructions");
            instructions.AppendLine("=================================================");
            instructions.AppendLine();
            instructions.AppendLine("1. Generate Private Key (Unencrypted):");
            instructions.AppendLine("   openssl genrsa 2048 | openssl pkcs8 -topk8 -inform PEM -out rsa_key.p8 -nocrypt");
            instructions.AppendLine();
            instructions.AppendLine("2. Generate Private Key (Encrypted - Recommended):");
            instructions.AppendLine("   openssl genrsa 2048 | openssl pkcs8 -topk8 -v2 des3 -inform PEM -out rsa_key.p8");
            instructions.AppendLine();
            instructions.AppendLine("3. Generate Public Key:");
            instructions.AppendLine("   openssl rsa -in rsa_key.p8 -pubout -out rsa_key.pub");
            instructions.AppendLine();
            instructions.AppendLine("4. Assign Public Key to Snowflake User:");
            instructions.AppendLine("   ALTER USER example_user SET RSA_PUBLIC_KEY='MIIBIjANBgkqh...';");
            instructions.AppendLine("   (Remove the -----BEGIN PUBLIC KEY----- and -----END PUBLIC KEY----- lines)");
            instructions.AppendLine();
            instructions.AppendLine("5. Configure Genesys Adapter:");
            instructions.AppendLine("   Set AuthenticationMethod to 'KeyPair'");
            instructions.AppendLine("   Set PrivateKeyFile to the path of your private key file, OR");
            instructions.AppendLine("   Set PrivateKeyContent to the content of your private key");
            instructions.AppendLine("   Set PrivateKeyPassphrase if your private key is encrypted");
            instructions.AppendLine();
            instructions.AppendLine("Security Recommendations:");
            instructions.AppendLine("- Use encrypted private keys with strong passphrases");
            instructions.AppendLine("- Store private key files with restricted permissions (600)");
            instructions.AppendLine("- Rotate keys regularly according to your security policy");
            instructions.AppendLine("- Never commit private keys to version control");

            return instructions.ToString();
        }

        /// <summary>
        /// Checks if a private key is encrypted
        /// </summary>
        /// <param name="privateKey">The private key content</param>
        /// <returns>True if the private key is encrypted</returns>
        public static bool IsPrivateKeyEncrypted(string privateKey)
        {
            if (string.IsNullOrWhiteSpace(privateKey))
                return false;

            return privateKey.Contains("BEGIN ENCRYPTED PRIVATE KEY");
        }

        /// <summary>
        /// Extracts the public key from a private key file
        /// </summary>
        /// <param name="privateKeyPath">Path to the private key file</param>
        /// <param name="passphrase">Passphrase for encrypted private keys (optional)</param>
        /// <returns>The public key content, or null if extraction fails</returns>
        public static string? ExtractPublicKeyFromPrivateKey(string privateKeyPath, string? passphrase = null)
        {
            try
            {
                if (!File.Exists(privateKeyPath))
                    return null;

                // This is a simplified implementation - in a real scenario, you would use
                // a proper cryptographic library to extract the public key
                // For now, we'll return instructions for manual extraction
                return "Use: openssl rsa -in " + privateKeyPath + " -pubout -out public_key.pub";
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Validates file permissions for private key files (Unix-like systems)
        /// </summary>
        /// <param name="filePath">Path to the private key file</param>
        /// <returns>True if file permissions are secure</returns>
        public static bool ValidateFilePermissions(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                // On Windows, this is less relevant, but we can check basic file attributes
                var fileInfo = new FileInfo(filePath);
                
                // Check if file is read-only or has restricted access
                return !fileInfo.Attributes.HasFlag(FileAttributes.ReadOnly) || 
                       fileInfo.Attributes.HasFlag(FileAttributes.Hidden);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets security recommendations for key pair management
        /// </summary>
        /// <returns>List of security recommendations</returns>
        public static string[] GetSecurityRecommendations()
        {
            return new[]
            {
                "Use 2048-bit or larger RSA keys",
                "Encrypt private keys with strong passphrases",
                "Store private keys with restricted file permissions (600 on Unix)",
                "Rotate key pairs regularly (every 90-180 days)",
                "Never store private keys in version control",
                "Use separate key pairs for different environments",
                "Monitor key usage and access logs",
                "Implement key escrow for business continuity",
                "Use hardware security modules (HSMs) for production environments",
                "Regularly audit key pair configurations"
            };
        }

        /// <summary>
        /// Checks if a database configuration is using legacy password authentication
        /// </summary>
        /// <param name="databaseOptions">Database configuration to check</param>
        /// <returns>True if using legacy password authentication</returns>
        public static bool IsLegacyPasswordConfiguration(Database databaseOptions)
        {
            return databaseOptions.AuthenticationMethod == null ||
                   databaseOptions.AuthenticationMethod == AuthenticationMethod.Password;
        }

        /// <summary>
        /// Validates that a configuration change won't break existing deployments
        /// </summary>
        /// <param name="currentConfig">Current database configuration</param>
        /// <param name="newConfig">New database configuration</param>
        /// <param name="logger">Optional logger for validation messages</param>
        /// <returns>True if the configuration change is safe</returns>
        public static bool ValidateConfigurationMigration(Database currentConfig, Database newConfig, ILogger? logger = null)
        {
            // If both configurations use the same authentication method, it's safe
            var currentAuth = currentConfig.AuthenticationMethod ?? AuthenticationMethod.Password;
            var newAuth = newConfig.AuthenticationMethod ?? AuthenticationMethod.Password;

            if (currentAuth == newAuth)
            {
                logger?.LogDebug("Configuration migration is safe - same authentication method");
                return true;
            }

            // Warn about authentication method changes
            logger?.LogWarning("Authentication method changing from {CurrentAuth} to {NewAuth}", currentAuth, newAuth);

            // Validate that the new configuration is complete
            switch (newAuth)
            {
                case AuthenticationMethod.KeyPair:
                    return ValidateKeyPairConfiguration(newConfig, logger);
                case AuthenticationMethod.OAuth:
                    if (string.IsNullOrEmpty(newConfig.OAuthToken))
                    {
                        logger?.LogError("OAuth token is required for OAuth authentication");
                        return false;
                    }
                    break;
                case AuthenticationMethod.Password:
                    if (string.IsNullOrEmpty(newConfig.Password))
                    {
                        logger?.LogError("Password is required for password authentication");
                        return false;
                    }
                    break;
            }

            return true;
        }

        /// <summary>
        /// Provides migration guidance for moving from password to key pair authentication
        /// </summary>
        /// <returns>Step-by-step migration instructions</returns>
        public static string GetMigrationGuidance()
        {
            var guidance = new StringBuilder();
            guidance.AppendLine("Migration from Password to Key Pair Authentication");
            guidance.AppendLine("================================================");
            guidance.AppendLine();
            guidance.AppendLine("IMPORTANT: Test this migration in a non-production environment first!");
            guidance.AppendLine();
            guidance.AppendLine("Step 1: Generate Key Pair");
            guidance.AppendLine("- Follow the key generation instructions");
            guidance.AppendLine("- Store private key securely");
            guidance.AppendLine();
            guidance.AppendLine("Step 2: Configure Snowflake User");
            guidance.AppendLine("- Assign public key to Snowflake user");
            guidance.AppendLine("- Test key pair authentication manually");
            guidance.AppendLine();
            guidance.AppendLine("Step 3: Update Configuration");
            guidance.AppendLine("- Set AuthenticationMethod to 'KeyPair'");
            guidance.AppendLine("- Set PrivateKeyFile or PrivateKeyContent");
            guidance.AppendLine("- Set PrivateKeyPassphrase if needed");
            guidance.AppendLine("- Keep Password field for rollback");
            guidance.AppendLine();
            guidance.AppendLine("Step 4: Test and Validate");
            guidance.AppendLine("- Test database connectivity");
            guidance.AppendLine("- Verify all jobs run successfully");
            guidance.AppendLine("- Monitor logs for authentication errors");
            guidance.AppendLine();
            guidance.AppendLine("Step 5: Rollback Plan");
            guidance.AppendLine("- Change AuthenticationMethod back to 'Password'");
            guidance.AppendLine("- Ensure Password field is still configured");
            guidance.AppendLine("- Remove key pair configuration if needed");

            return guidance.ToString();
        }
    }
}
