<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RuntimeIdentifiers>win-x64;linux-x64;linux-musl-x64</RuntimeIdentifiers>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <AnalysisLevel>latest</AnalysisLevel>
    <!-- Disable GitVersion for this project to prevent duplicate class generation -->
    <DisableGitVersionTask>true</DisableGitVersionTask>
    <GitVersionTaskEnabled>false</GitVersionTaskEnabled>
    <!-- Disable assembly attribute generation to prevent duplicates -->
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
  </PropertyGroup>

  <!-- Remove GitVersion package reference to prevent it from running -->
  <ItemGroup>
    <PackageReference Remove="GitVersion.MsBuild" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="3.1.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.1.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.4.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GenesysAdapter\GenesysAdapter.csproj" />
    <ProjectReference Include="..\GenesysCloudUtils\GenesysCloudUtils.csproj" />
    <ProjectReference Include="..\DBUtils\DBUtils.csproj" />
    <ProjectReference Include="..\GCACommon\GCACommon.csproj" />
  </ItemGroup>

</Project>
<!-- spell-checker: ignore analyzers buildtransitive contentfiles -->