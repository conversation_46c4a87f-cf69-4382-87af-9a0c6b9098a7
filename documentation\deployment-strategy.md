# Snowflake Key Pair Authentication Deployment Strategy

This document outlines the deployment strategy for implementing Snowflake key pair authentication in the Genesys Adapter, including migration procedures, rollback plans, and validation scripts.

## Overview

The deployment strategy ensures a smooth transition from password-based to key pair authentication for Snowflake connections while maintaining system availability and data integrity.

## Pre-Deployment Requirements

### 1. Snowflake Setup
- Generate RSA key pairs for each environment (development, staging, production)
- Configure public keys in Snowflake user accounts
- Test key pair authentication in non-production environments
- Document key storage locations and access procedures

### 2. Infrastructure Preparation
- Secure key storage solution (Azure Key Vault, AWS Secrets Manager, etc.)
- Backup current configuration files
- Prepare monitoring and alerting for authentication failures
- Schedule maintenance windows for production deployment

### 3. Team Preparation
- Train operations team on new authentication method
- Prepare troubleshooting documentation
- Establish communication channels for deployment coordination
- Review rollback procedures with all stakeholders

## Deployment Phases

### Phase 1: Development Environment (Week 1)
1. Deploy new Genesys Adapter version with key pair support
2. Configure key pair authentication alongside existing password authentication
3. Run comprehensive testing suite
4. Validate all job types work correctly
5. Performance testing and monitoring setup

### Phase 2: Staging Environment (Week 2)
1. Deploy to staging environment
2. Run full integration tests
3. Test failover scenarios
4. Validate monitoring and alerting
5. Conduct user acceptance testing

### Phase 3: Production Deployment (Week 3)
1. Deploy during scheduled maintenance window
2. Configure key pair authentication
3. Monitor system health and performance
4. Gradual rollout to all production instances
5. Post-deployment validation

## Configuration Migration

### Step 1: Backup Current Configuration
```bash
# Backup existing configuration
cp appsettings.json appsettings.json.backup.$(date +%Y%m%d_%H%M%S)
cp appsettings.Production.json appsettings.Production.json.backup.$(date +%Y%m%d_%H%M%S)
```

### Step 2: Update Configuration Files
```json
{
  "Database": {
    "Type": "Snowflake",
    "Address": "your-account.snowflakecomputing.com",
    "Name": "your_database",
    "User": "your_user",
    "AuthenticationMethod": "KeyPair",
    "PrivateKeyFile": "/path/to/private/key.pem",
    "PrivateKeyPassphrase": "your_passphrase"
  }
}
```

### Step 3: Validate Configuration
```bash
# Run configuration validation
./GenesysAdapter --validate-config
```

## Validation Scripts

### Pre-Deployment Validation
```powershell
# validate-pre-deployment.ps1
param(
    [string]$ConfigPath = "appsettings.json"
)

Write-Host "Validating pre-deployment requirements..."

# Check if configuration file exists
if (-not (Test-Path $ConfigPath)) {
    Write-Error "Configuration file not found: $ConfigPath"
    exit 1
}

# Validate JSON format
try {
    $config = Get-Content $ConfigPath | ConvertFrom-Json
    Write-Host "✓ Configuration file is valid JSON"
} catch {
    Write-Error "✗ Configuration file is not valid JSON: $_"
    exit 1
}

# Check database configuration
if ($config.Database.Type -eq "Snowflake") {
    if ($config.Database.AuthenticationMethod -eq "KeyPair") {
        if ([string]::IsNullOrEmpty($config.Database.PrivateKeyFile) -and 
            [string]::IsNullOrEmpty($config.Database.PrivateKeyContent)) {
            Write-Error "✗ Private key file or content must be specified for key pair authentication"
            exit 1
        }
        Write-Host "✓ Snowflake key pair authentication configuration is valid"
    }
}

Write-Host "Pre-deployment validation completed successfully"
```

### Post-Deployment Validation
```powershell
# validate-post-deployment.ps1
param(
    [string]$ConfigPath = "appsettings.json",
    [int]$TimeoutSeconds = 300
)

Write-Host "Validating post-deployment functionality..."

# Test database connection
Write-Host "Testing database connection..."
$testResult = & ./GenesysAdapter --test-connection --timeout $TimeoutSeconds

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Database connection test passed"
} else {
    Write-Error "✗ Database connection test failed"
    exit 1
}

# Test basic job execution
Write-Host "Testing basic job execution..."
$jobResult = & ./GenesysAdapter --Job=Information --timeout 60

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Basic job execution test passed"
} else {
    Write-Error "✗ Basic job execution test failed"
    exit 1
}

Write-Host "Post-deployment validation completed successfully"
```

## Rollback Procedures

### Immediate Rollback (< 1 hour)
1. **Stop Genesys Adapter services**
   ```bash
   systemctl stop genesys-adapter
   ```

2. **Restore previous configuration**
   ```bash
   cp appsettings.json.backup.* appsettings.json
   ```

3. **Restart services with previous version**
   ```bash
   systemctl start genesys-adapter
   ```

4. **Validate system functionality**
   ```bash
   ./validate-post-deployment.ps1
   ```

### Extended Rollback (> 1 hour)
1. **Deploy previous Genesys Adapter version**
2. **Restore database configuration**
3. **Run full system validation**
4. **Update monitoring and alerting**
5. **Notify stakeholders of rollback completion**

## Monitoring and Alerting

### Key Metrics to Monitor
- Authentication success/failure rates
- Connection establishment time
- Job execution success rates
- System resource utilization
- Error log patterns

### Alert Thresholds
- Authentication failure rate > 5%
- Connection time > 30 seconds
- Job failure rate > 10%
- Error log entries containing "authentication" or "key pair"

### Monitoring Queries
```sql
-- Monitor authentication failures
SELECT 
    DATE_TRUNC('hour', timestamp) as hour,
    COUNT(*) as failure_count
FROM logs 
WHERE level = 'ERROR' 
    AND message LIKE '%authentication%'
    AND timestamp > NOW() - INTERVAL '24 hours'
GROUP BY hour
ORDER BY hour DESC;
```

## Risk Mitigation

### High Risk Scenarios
1. **Private key corruption or loss**
   - Mitigation: Multiple backup copies in secure locations
   - Recovery: Emergency key regeneration procedure

2. **Snowflake user account lockout**
   - Mitigation: Multiple service accounts with key pair access
   - Recovery: Emergency password authentication fallback

3. **Configuration file corruption**
   - Mitigation: Automated configuration backups
   - Recovery: Configuration restoration from backup

### Medium Risk Scenarios
1. **Network connectivity issues**
   - Mitigation: Connection retry logic and timeouts
   - Recovery: Network troubleshooting procedures

2. **Performance degradation**
   - Mitigation: Performance monitoring and alerting
   - Recovery: Resource scaling procedures

## Success Criteria

### Deployment Success
- [ ] All Genesys Adapter instances successfully authenticate using key pairs
- [ ] All job types execute without authentication errors
- [ ] System performance meets baseline requirements
- [ ] No data loss or corruption
- [ ] Monitoring and alerting systems operational

### Post-Deployment (24 hours)
- [ ] Authentication success rate > 99%
- [ ] Job execution success rate maintains baseline
- [ ] No critical errors in system logs
- [ ] Performance metrics within acceptable ranges
- [ ] Team comfortable with new authentication method

## Communication Plan

### Stakeholder Notifications
1. **Pre-deployment (1 week prior)**
   - Deployment schedule and impact assessment
   - Required actions for each team
   - Contact information for support

2. **During deployment**
   - Real-time status updates
   - Any issues or delays
   - Estimated completion times

3. **Post-deployment**
   - Deployment completion confirmation
   - System status and performance summary
   - Next steps and ongoing monitoring

### Escalation Procedures
1. **Level 1**: Operations team handles routine issues
2. **Level 2**: Development team for authentication-specific problems
3. **Level 3**: Architecture team for system-wide issues
4. **Level 4**: Executive team for business-critical failures

## Conclusion

This deployment strategy provides a comprehensive approach to implementing Snowflake key pair authentication while minimizing risks and ensuring system reliability. Regular review and updates of this strategy will ensure continued effectiveness as the system evolves.
